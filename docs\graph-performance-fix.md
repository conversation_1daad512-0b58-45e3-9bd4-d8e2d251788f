# 图组件性能优化修复说明

## 问题描述

1. **Radical布局模式拖拽卡死**：在使用径向布局时，拖拽节点会导致页面无响应
2. **麒麟系统布局切换卡死**：在麒麟操作系统下切换布局时页面卡死

## 修复方案

### 1. 布局切换优化

#### 主要改进：
- **防抖处理**：避免快速连续切换布局导致的冲突
- **分步执行**：将布局计算分解为多个步骤，使用 `requestAnimationFrame` 和 `setTimeout` 避免阻塞UI
- **系统适配**：针对麒麟系统使用更保守的性能配置

#### 关键代码：
```javascript
const switchLayout = async (type) => {
  // 防抖处理
  if (switchLayout.pending) return;
  switchLayout.pending = true;
  
  try {
    const perfConfig = getPerformanceConfig();
    const isKylin = isKylinOS();
    
    // 麒麟系统下增加延迟
    const delay = isKylin ? 50 : 16;
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // 根据系统调整参数
    const nodeSep = isKylin ? 100 : 130;
    const rankSep = isKylin ? 150 : 200;
    
    // 特殊处理radical布局
    if (type === "radial") {
      const radialConfig = {
        type: "dendrogram",
        radial: true,
        nodeSep: isKylin ? 80 : nodeSep,
        rankSep: isKylin ? 120 : rankSep,
        animate: false, // 始终禁用动画
      };
      g.setLayout(radialConfig);
    }
    
    // 分步执行
    await g.layout();
    await new Promise(resolve => setTimeout(resolve, isKylin ? 100 : 16));
    
    // 温和的fitView
    if (isKylin) {
      g.fitView(48, { animation: false });
    } else {
      g.fitView(24);
    }
  } finally {
    switchLayout.pending = false;
  }
};
```

### 2. 拖拽性能优化

#### 主要改进：
- **节流处理**：根据系统性能调整更新频率
- **状态管理**：使用 `requestAnimationFrame` 确保状态正确重置
- **特殊优化**：在radical布局下暂时禁用某些功能

#### 关键代码：
```javascript
g.on("node:dragstart", () => {
  isDragging.value = true;
  dragStartTime = Date.now();
  
  // 在radical布局下暂时禁用缩放
  if (g.getLayoutType?.() === "dendrogram" && g.getLayoutConfig?.()?.radial) {
    g.updateBehavior({ key: "zoom-canvas", enable: false });
  }
});

g.on("node:drag", () => {
  if (dragTimeout) return;
  
  const perfConfig = getPerformanceConfig();
  dragTimeout = setTimeout(() => {
    dragTimeout = null;
  }, perfConfig.throttleDelay); // 根据系统调整频率
});

g.on("node:dragend", () => {
  requestAnimationFrame(() => {
    isDragging.value = false;
    // 重新启用功能
    g.updateBehavior({ key: "zoom-canvas", enable: (evt) => evt?.targetType === "canvas" });
  });
});
```

### 3. 系统兼容性检测

#### 麒麟系统检测：
```javascript
const isKylinOS = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes("kylin") || 
         userAgent.includes("neokylin") || 
         userAgent.includes("ubuntukylin") ||
         process.env.NODE_ENV === "kylin";
};
```

#### 性能配置：
```javascript
const getPerformanceConfig = () => {
  const isKylin = isKylinOS();
  return {
    animationDuration: isKylin ? 0 : 300,
    enableAnimation: !isKylin,
    throttleDelay: isKylin ? 32 : 16,
    maxNodes: isKylin ? 500 : 1000,
    useWebGL: !isKylin,
  };
};
```

### 4. 内存泄漏防护

#### 组件销毁时的清理：
```javascript
onBeforeUnmount(() => {
  const g = graphRef.value;
  if (g) {
    // 清除定时器
    if (dragTimeout) {
      clearTimeout(dragTimeout);
    }
    
    // 移除事件监听器
    g.off("node:dragstart");
    g.off("node:drag");
    g.off("node:dragend");
    g.off("canvas:mouseup");
    
    // 清空状态
    clearAllStates(g);
    
    // 销毁实例
    g.destroy();
  }
});
```

## 性能监控

使用 `src/utils/performance-test.js` 中的工具进行性能监控：

```javascript
import { performanceMonitor } from '@/utils/performance-test';

// 监控布局切换
performanceMonitor.startLayoutSwitch('radial');
await switchLayout('radial');
performanceMonitor.endLayoutSwitch();

// 检查内存使用
performanceMonitor.checkMemoryUsage();

// 获取性能报告
const report = performanceMonitor.getReport();
console.log(report);
```

## 测试建议

1. **在麒麟系统下测试**：
   - 快速切换布局（tree → dendrogram → radial）
   - 长时间拖拽节点
   - 大量节点的场景

2. **性能基准测试**：
   - 布局切换时间应 < 1秒
   - 拖拽响应时间应 < 100ms
   - 内存使用应稳定，无明显泄漏

3. **兼容性测试**：
   - Windows 10/11
   - macOS
   - Ubuntu
   - 麒麟系统各版本

## 注意事项

1. 在麒麟系统下，动画被自动禁用以提高性能
2. Radical布局下的节点间距会根据系统性能自动调整
3. 拖拽频率会根据系统性能自动节流
4. 建议在麒麟系统下限制节点数量在500个以内
