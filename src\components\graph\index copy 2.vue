<template>
  <div class="wrap" :class="{ 'drag-mode': isDragging }">
    <div id="graph-container" class="graph"></div>

    <div class="toolbar">
      <el-button @click="switchLayout('tree')">垂直布局</el-button>
      <el-button @click="switchLayout('dendrogram')">生态树布局</el-button>
      <el-button @click="switchLayout('radial')">生态图布局</el-button>
    </div>

    <!-- 右键信息面板（可选） -->
    <div v-if="panel.visible" class="info-panel" @contextmenu.prevent>
      <el-card>
        <div class="panel-hd">
          <div class="title">{{ panel.title }}</div>
          <div class="sub">{{ panel.sub }}</div>
          <el-button class="close" @click="panel.visible = false">×</el-button>
        </div>
        <div class="panel-bd">
          <div class="kv" v-for="(r, i) in panel.attrs" :key="i">
            <div class="k">{{ r.name ?? r.code }}</div>
            <div class="v">{{ r.value }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive, watch } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";

/* ========== Props ========== */
const props = defineProps({ data: { type: Object, default: () => ({}) } });

/* ========== 状态 ========== */
const graphRef = ref(null);
const isDragging = ref(false);
const lastSig = ref(null);
const colorAuto = ref({});
const panel = reactive({ visible: false, title: "", sub: "", attrs: [] });

/* ========== 配色 ========== */
const COLOR_BY_TYPE = {
  unit: { fill: "#FFC857", stroke: "#B8860B" },
  person: { fill: "#4F8DF7", stroke: "#1F5CC4" },
  train: { fill: "#33C08D", stroke: "#1E8A62" },
  other: { fill: "#a9a9a9", stroke: "#6e6e6e" },
};
const PALETTE = [
  { fill: "#FFC857", stroke: "#B8860B" },
  { fill: "#4F8DF7", stroke: "#1F5CC4" },
  { fill: "#33C08D", stroke: "#1E8A62" },
  { fill: "#A47CF3", stroke: "#6A4FB6" },
  { fill: "#FF7A90", stroke: "#C94A5A" },
  { fill: "#8BD3E6", stroke: "#4E98A8" },
];

/** 自动配色 */
function computeColorAuto(gData) {
  const types = Array.from(new Set((gData.nodes || []).map((n) => n.data?.type || "other")));
  const map = {};
  let i = 0;
  for (const t of types) map[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  return map;
}

/* ========== 数据→树→gData ========== */
function buildForestFromLinks(raw, options = {}) {
  const { reverseByRelation = new Set(), maxDepth = 16 } = options;
  const nodes = raw?.nodes || [];
  const links = raw?.links || [];
  const nodeById = new Map(nodes.map((n) => [String(n.id), n]));

  const getTypeKey = (n) => n?.labels?.[0] || n?.label || "other";
  const getAttr = (n, code) => n?.attrs?.find((a) => a.code === code || a.name === code)?.value ?? "";
  const getName = (n) => n?.nodeName || getAttr(n, "name") || n?.entityId || n?.id || "";
  const makeId = (type, id, ctx) => (ctx ? `${type}:${id}@${ctx}` : `${type}:${id}`);
  const toTreeNode = (n, type, ctx, parentRelation) => ({
    id: makeId(type, String(n.id), ctx),
    name: getName(n),
    collapsed: false,
    data: {
      originId: String(n.id),
      entityId: n.entityId,
      labels: n.labels,
      type,
      attrs: n.attrs,
      parentRelation: parentRelation || "",
    },
  });

  const out = new Map();
  const indeg = new Map();
  for (const e of links) {
    const rel = e.name || "";
    let s = String(e.source),
      t = String(e.target);
    if (reverseByRelation.has(rel)) [s, t] = [t, s];
    if (!out.has(s)) out.set(s, []);
    out.get(s).push({ to: t, relation: rel });
    indeg.set(t, (indeg.get(t) || 0) + 1);
  }

  const allIds = nodes.map((n) => String(n.id));
  const rootIds = allIds.filter((id) => !indeg.get(id));

  function dfs(curId, ctx, depth, stack) {
    const n = nodeById.get(curId);
    if (!n) return null;
    const node = toTreeNode(n, n?.labels?.[0] || n?.label || "other", ctx, stack?.parentRelation);
    if (depth >= maxDepth) return node;
    const seen = stack?.set || new Set();
    if (seen.has(curId)) return node;
    const nextSeen = new Set(seen);
    nextSeen.add(curId);
    const children = [];
    for (const e of out.get(curId) || []) {
      const child = dfs(e.to, `p:${curId}`, depth + 1, { set: nextSeen, parentRelation: e.relation });
      if (child) children.push(child);
    }
    if (children.length) node.children = children;
    return node;
  }

  if (rootIds.length) {
    const forest = [];
    for (const rid of rootIds) forest.push(dfs(rid, "", 0, { set: new Set(), parentRelation: "" }));
    return forest.filter(Boolean);
  }

  const virtualChildren = [];
  for (const id of allIds) {
    const sub = dfs(id, "root", 0, { set: new Set(), parentRelation: "" });
    if (sub) virtualChildren.push(sub);
  }
  return [{ id: "ROOT", name: "ROOT", data: { type: "other" }, children: virtualChildren }];
}

/** 树→gData */
function makeGraphData(raw) {
  const forests = buildForestFromLinks(raw);
  const treeRoot = forests.length === 1 ? forests[0] : { id: "ROOT", name: "ROOT", children: forests };
  const gData = treeToGraphData(treeRoot);

  const nodeIndex = new Map((gData.nodes || []).map((n) => [String(n.id), n]));
  gData.edges = (gData.edges || []).map((e, idx) => {
    const relation = nodeIndex.get(String(e.target))?.data?.parentRelation || "";
    const id = e.id || `e-${idx}-${e.source}-${e.target}`; // ★ 边补 id
    return { ...e, id, data: { ...(e.data || {}), relation } };
  });
  return gData;
}

/* ========== 样式与布局 ========== */
const EDGE_STYLE = {
  endArrow: true,
  labelText: (d) => d?.data?.relation ?? "",
  labelPlacement: "center",
  labelAutoRotate: false,
  labelBackground: true,
  labelPadding: [2, 6, 2, 6],
};

/** 是否为叶节点 */
function isLeafNode(d) {
  return !d?.children || d.children.length === 0;
}

/** 切换布局 */
const switchLayout = (type) => {
  const g = graphRef.value;
  if (!g) return;
  if (type === "tree") {
    g.setLayout({ type: "dendrogram", direction: "TB", nodeSep: 150, rankSep: 200 });
    g.setEdge({ type: "cubic-vertical", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "dendrogram") {
    g.setLayout({ type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "cubic-horizontal", style: EDGE_STYLE, animation: { enter: false } });
  } else if (type === "radial") {
    g.setLayout({ type: "dendrogram", radial: true, nodeSep: 130, rankSep: 200 });
    g.setEdge({ type: "line", style: EDGE_STYLE, animation: { enter: false } });
  }
  g.layout();
  g.fitView(24);
};

/* ========== 数据签名（避免无谓刷新） ========== */
function makeDataSignature(d) {
  const nodes = d?.nodes ?? [];
  const links = d?.links ?? [];
  let h = 0x811c9dc5;
  const mix = (s) => {
    for (let i = 0; i < s.length; i++) {
      h ^= s.charCodeAt(i);
      h = (h >>> 0) * 16777619;
    }
  };
  mix(String(nodes.length));
  for (const n of nodes) mix(`${n.id}|${n.updatedAt ?? n.version ?? ""}`);
  mix(String(links.length));
  for (const e of links) mix(`${e.source}->${e.target}|${e.name ?? ""}`);
  return h >>> 0;
}

/* ========== 清空所有状态（还原样式） ========== */
function clearAllStates(g) {
  const map = {};
  g.getNodeData().forEach((n) => {
    map[n.id] = [];
  });
  g.getEdgeData().forEach((e) => {
    map[e.id] = [];
  });
  g.setElementState(map);
}

/* ========== props.data → 图上（v5） ========== */
async function updateGraphFromProps(raw) {
  const g = graphRef.value;
  if (!g) return;
  const gData = makeGraphData(raw);
  colorAuto.value = computeColorAuto(gData);

  g.setData(gData);
  // 让颜色映射变更立即生效（更新节点样式函数）
  g.setNode({
    type: "rect",
    style: (d) => {
      const t = d?.data?.type ?? "other";
      const { fill, stroke } = colorAuto.value[t] || COLOR_BY_TYPE.other;
      return {
        fill,
        stroke,
        lineWidth: 1.5,
        radius: 20,
        padding: [4, 8, 4, 8],
        labelText: `${d.name ?? d.id}`,
        labelFill: "#fff",
        labelPlacement: isLeafNode(d) ? "right" : "left",
        labelBackground: true,
      };
    },
    animation: { enter: false },
  });
  await g.render();
  g.fitView(24);
}

/* ========== 初始化 ========== */
onMounted(async () => {
  const gData = makeGraphData(props.data || {});
  colorAuto.value = computeColorAuto(gData);

  const g = (graphRef.value = new Graph({
    container: "graph-container",
    devicePixelRatio: window.devicePixelRatio,
    autoFit: "view",
    data: gData,
    node: {
      type: "rect",
      style: (d) => {
        const t = d?.data?.type ?? "other";
        const { fill, stroke } = colorAuto.value[t] || COLOR_BY_TYPE.other;
        return {
          fill,
          stroke,
          lineWidth: 1.5,
          radius: 20,
          padding: [4, 8, 4, 8],
          labelText: `${d.name ?? d.id}`,
          labelFill: "#fff",
          labelPlacement: isLeafNode(d) ? "right" : "left",
          labelBackground: true,
        };
      },
      animation: { enter: false },
    },
    edge: {
      type: "cubic-horizontal",
      style: EDGE_STYLE,
      animation: { enter: false },
    },
    layout: { type: "dendrogram", direction: "LR", nodeSep: 130, rankSep: 200 },

    /* 交互：避免冲突 + 高亮邻居 */
    behaviors: [
      // 仅空白画布可拖画布；节点拖拽时禁用
      { type: "drag-canvas", key: "drag-canvas", enable: (evt) => !isDragging.value && evt?.targetType === "canvas" },
      // 缩放：如果希望节点上滚轮也缩放，把 enable 改成 ()=>true
      { type: "zoom-canvas", key: "zoom-canvas", enable: (evt) => evt?.targetType === "canvas" },
      // 展开/收起仅在点节点时生效
      { type: "collapse-expand", trigger: "click", shouldBegin: (evt) => evt?.targetType === "node" },
      // 拖节点：拖拽期间隐藏所有边
      {
        type: "drag-element",
        key: "drag-node",
        enable: (evt) => evt?.targetType === "node",
        dropEffect: "move",
        hideEdge: "all",
        shadow: false,
        cursor: { grab: "grab", grabbing: "grabbing" },
      },
    ],

    plugins: [
      // 右键查看详情（可选）
      {
        type: "contextmenu",
        getItems: () => [], // 不显示默认菜单
      },
      // {
      //   type: "tooltip",
      //   key: "node-tooltip",
      //   itemTypes: ["node"],
      //   trigger: "hover",
      //   shouldBegin: (e) => !isDragging.value && e?.target?.type === "node",
      //   getContent: (e) => {
      //     if (e.targetType !== "node") return "";
      //     const id = e?.target?.id;
      //     if (!id) return "";
      //     const nd = g.getNodeData(id);
      //     const attrs = nd?.data?.attrs || [];
      //     const rows = attrs.map((a) => `<div class="row"><b>${a.name ?? a.code}</b>：${a.value ?? ""}</div>`).join("");
      //     return `<div class="g6-tip"><div class="hd">${nd?.name ?? id}</div>${rows || "<i>无属性</i>"}</div>`;
      //   },
      //   offsetX: 12,
      //   offsetY: 8,
      // },
      { type: "minimap", size: [240, 160] },
      { type: "fullscreen", key: "fullscreen" },
    ],
    theme: "dark",
  }));

  // 拖拽态标记（避免与拖画布冲突）
  g.on("node:dragstart", () => {
    isDragging.value = true;
  });
  g.on("node:dragend", () => {
    isDragging.value = false;
  });
  g.on("canvas:mouseup", () => {
    isDragging.value = false;
  });

  // 右键显示信息面板（可选）
  g.on("node:contextmenu", (evt) => {
    evt.originalEvent?.preventDefault?.();
    const id = evt?.target?.id;
    if (!id) return;
    const nd = g.getNodeData(id);
    panel.title = nd?.name ?? id;
    panel.sub = nd?.data?.type ?? nd?.data?.labels?.[0] ?? "";
    panel.attrs = nd?.data?.attrs ?? [];
    panel.visible = true;
  });
  g.on("canvas:click", () => {
    panel.visible = false;
    clearAllStates(g);
  }); // 点空白清空高亮

  await g.render();
  g.fitView(24);
  lastSig.value = makeDataSignature(props.data || {});
});

/* ========== 销毁 ========== */
onBeforeUnmount(() => {
  graphRef.value?.destroy();
  graphRef.value = null;
});

/* ========== 监听外部数据变化 ========== */
watch(
  () => props.data,
  async (raw) => {
    const sig = makeDataSignature(raw || {});
    if (sig === lastSig.value) return;
    lastSig.value = sig;
    await updateGraphFromProps(raw || {});
  },
  { deep: true, flush: "post" }
);
</script>

<style scoped>
.wrap {
  position: relative;
  width: 100%;
  height: 100%;
}
.toolbar {
  display: flex;
  gap: 8px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}
.graph {
  width: 100%;
  height: 100%;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

/* 拖拽时可淡出面板/工具栏，避免干扰（可选） */
.drag-mode .toolbar,
.drag-mode .info-panel {
  opacity: 0.4;
  pointer-events: none;
}

/* 信息面板 */
.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
  z-index: 12;
}
.panel-hd {
  display: flex;
  align-items: start;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #409eff;
  word-break: break-all;
}
.v {
  color: #000;
  word-break: break-all;
}

/* Tooltip */
.g6-tip {
  max-width: 320px;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  border: 1px solid #333;
  padding: 8px 10px;
  border-radius: 6px;
}
.g6-tip .hd {
  font-weight: 700;
  margin-bottom: 6px;
}
.g6-tip .row {
  font-size: 12px;
  margin: 2px 0;
}
</style>
