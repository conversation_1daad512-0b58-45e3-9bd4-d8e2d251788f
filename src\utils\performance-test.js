/**
 * 性能测试工具
 * 用于测试图组件在不同系统下的性能表现
 */

// 系统检测
export const detectOS = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return {
    isKylin: userAgent.includes('kylin') || userAgent.includes('neokylin') || userAgent.includes('ubuntukylin'),
    isWindows: userAgent.includes('windows'),
    isMac: userAgent.includes('mac'),
    isLinux: userAgent.includes('linux') && !userAgent.includes('kylin'),
  };
};

// 性能监控
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      layoutSwitchTime: [],
      dragPerformance: [],
      renderTime: [],
      memoryUsage: [],
    };
  }

  // 开始监控布局切换性能
  startLayoutSwitch(layoutType) {
    this.layoutSwitchStart = performance.now();
    this.currentLayout = layoutType;
    console.log(`开始切换到 ${layoutType} 布局`);
  }

  // 结束监控布局切换性能
  endLayoutSwitch() {
    if (this.layoutSwitchStart) {
      const duration = performance.now() - this.layoutSwitchStart;
      this.metrics.layoutSwitchTime.push({
        layout: this.currentLayout,
        duration,
        timestamp: Date.now(),
      });
      console.log(`${this.currentLayout} 布局切换耗时: ${duration.toFixed(2)}ms`);
      this.layoutSwitchStart = null;
    }
  }

  // 监控拖拽性能
  monitorDragPerformance(startTime, endTime, nodeCount) {
    const duration = endTime - startTime;
    this.metrics.dragPerformance.push({
      duration,
      nodeCount,
      timestamp: Date.now(),
    });
    console.log(`拖拽操作耗时: ${duration.toFixed(2)}ms, 节点数: ${nodeCount}`);
  }

  // 监控内存使用
  checkMemoryUsage() {
    if (performance.memory) {
      const memory = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        timestamp: Date.now(),
      };
      this.metrics.memoryUsage.push(memory);
      console.log(`内存使用: ${(memory.used / 1024 / 1024).toFixed(2)}MB / ${(memory.total / 1024 / 1024).toFixed(2)}MB`);
      return memory;
    }
    return null;
  }

  // 获取性能报告
  getReport() {
    const os = detectOS();
    return {
      system: os,
      metrics: this.metrics,
      summary: {
        avgLayoutSwitchTime: this.getAverage(this.metrics.layoutSwitchTime.map(m => m.duration)),
        avgDragTime: this.getAverage(this.metrics.dragPerformance.map(m => m.duration)),
        peakMemoryUsage: Math.max(...this.metrics.memoryUsage.map(m => m.used)),
      },
    };
  }

  // 计算平均值
  getAverage(arr) {
    return arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0;
  }

  // 清空监控数据
  clear() {
    this.metrics = {
      layoutSwitchTime: [],
      dragPerformance: [],
      renderTime: [],
      memoryUsage: [],
    };
  }
}

// 性能优化建议
export const getPerformanceRecommendations = (report) => {
  const recommendations = [];
  const { system, summary } = report;

  if (system.isKylin) {
    recommendations.push('检测到麒麟系统，建议禁用动画以提高性能');
    recommendations.push('建议减少节点数量，限制在500个以内');
  }

  if (summary.avgLayoutSwitchTime > 1000) {
    recommendations.push('布局切换时间过长，建议优化布局算法');
  }

  if (summary.avgDragTime > 100) {
    recommendations.push('拖拽响应时间过长，建议增加节流延迟');
  }

  if (summary.peakMemoryUsage > 100 * 1024 * 1024) { // 100MB
    recommendations.push('内存使用过高，建议清理未使用的资源');
  }

  return recommendations;
};

// 导出单例实例
export const performanceMonitor = new PerformanceMonitor();
